import os
import requests
import google.generativeai as genai
from datetime import datetime
import json
from dotenv import load_dotenv
load_dotenv()

def escape_markdown(text):
    """Escape special Markdown characters"""
    escape_chars = '_*[]()~`>#+-=|{}.!'
    return ''.join(f'\\{char}' if char in escape_chars else char for char in text)

def send_telegram_message(bot_token, chat_id, message):
    """Send message to Telegram chat"""
    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    
    # Escape Markdown special characters
    message = escape_markdown(message)
    
    # Split long messages if needed (Telegram has 4096 char limit)
    if len(message) > 4000:
        chunks = [message[i:i+4000] for i in range(0, len(message), 4000)]
        for chunk in chunks:
            payload = {
                'chat_id': chat_id,
                'text': chunk,
                'parse_mode': 'MarkdownV2',
                'disable_web_page_preview': True
            }
            response = requests.post(url, json=payload)
            if not response.ok:
                print(f"Failed to send message chunk: {response.text}")
    else:
        payload = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'MarkdownV2',
            'disable_web_page_preview': True
        }
        response = requests.post(url, json=payload)
        if response.ok:
            print("Message sent successfully!")
        else:
            print(f"Failed to send message: {response.text}")

def format_grounding_info(response):
    """Format grounding information from the response."""
    if not hasattr(response, 'candidates') or not response.candidates:
        return ""
    
    candidate = response.candidates[0]
    if not hasattr(candidate, 'grounding_metadata'):
        return ""
    
    grounding = candidate.grounding_metadata
    if not hasattr(grounding, 'grounding_chunks'):
        return ""
    
    sources = []
    for i, chunk in enumerate(grounding.grounding_chunks):
        if chunk.web:
            sources.append(f"{i+1}. {chunk.web.title} ({chunk.web.uri})")
    
    if not sources:
        return ""
        
    return "\n\nSources:\n" + "\n".join(sources)

def get_gemini_response(api_key, prompt):
    try:
        # Configure the API key
        genai.configure(api_key=api_key)

        # Define the generation configuration
        generation_config = genai.GenerationConfig(
            temperature=0.7,
            top_p=0.9,
            top_k=40,
            max_output_tokens=2048
        )

        # Try to create model with Google Search tool, fallback to basic model if not available
        try:
            model = genai.GenerativeModel(
                model_name='gemini-1.5-flash',
                generation_config=generation_config,
                tools='google_search_retrieval'
            )
            print("DEBUG: Using model with Google Search tool")
        except Exception as tool_error:
            print(f"DEBUG: Google Search tool not available ({tool_error}), using basic model")
            model = genai.GenerativeModel(
                model_name='gemini-1.5-flash',
                generation_config=generation_config
            )

        response = model.generate_content(prompt)

        # Get the response text and add grounding info if available
        response_text = response.text
        grounding_info = format_grounding_info(response)

        return response_text + grounding_info
    except Exception as e:
        error_msg = f"Error getting Gemini response: {str(e)}"
        print(f"DEBUG: {error_msg}")  # Add debug logging
        return error_msg

def create_daily_prompt():
    """Create a daily prompt - customize this function"""
    today = datetime.now().strftime("%B %d, %Y")
    
    # prompts = [
    #     f"Give me 3 interesting tech facts for {today}",
    #     f"What's a productivity tip for {today}?",
    #     f"Share an inspiring quote and explain why it's meaningful for {today}",
    #     f"What's happening in the world of AI today, {today}?",
    #     f"Give me a brief summary of an interesting scientific discovery and its implications for {today}"
    # ]
    
    # You can rotate prompts or use random selection
    # import random
    # return random.choice(prompts)
    
    prompt = f"Give brief summary of today's news from Indonesia."

    return prompt

def main():
    # Get environment variables
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID')
    gemini_api_key = os.getenv('GEMINI_API_KEY')
    
    if not all([bot_token, chat_id, gemini_api_key]):
        print("Missing required environment variables!")
        return
    
    # Create daily prompt
    prompt = create_daily_prompt()
    
    # Get Gemini response
    print(f"Getting Gemini response for prompt: {prompt}")
    gemini_response = get_gemini_response(gemini_api_key, prompt)
    
    # Format message
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
    message = f"""🤖 **Daily Gemini Update**
📅 {current_time}

**Prompt:** {prompt}

**Response:**
{gemini_response}

---
_Sent automatically via GitHub Actions_"""
    
    # Send to Telegram
    send_telegram_message(bot_token, chat_id, message)

if __name__ == "__main__":
    main()