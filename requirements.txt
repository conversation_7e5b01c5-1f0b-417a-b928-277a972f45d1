agent @ file:///Users/<USER>/Documents/GitHub/gemini-fullstack-langgraph-quickstart/backend
aiocache==0.12.3
aiofiles==24.1.0
aiohappyeyeballs==2.4.6
aiohttp==3.11.11
aiosignal==1.3.2
alembic==1.14.0
alpha_vantage==3.0.0
altair==5.3.0
amqp==5.3.1
annotated-types==0.6.0
anthropic==0.25.8
anyio==4.8.0
apispec==6.7.1
appdirs==1.4.4
appnope==0.1.3
APScheduler==3.10.4
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asgiref==3.8.1
asttokens==2.2.1
async-timeout==5.0.1
attrs==23.1.0
Authlib==1.4.1
av==14.2.0
azure-ai-documentintelligence==1.0.0
azure-core==1.32.0
azure-identity==1.20.0
azure-storage-blob==12.24.1
backcall==0.2.0
backoff==2.2.1
bcrypt==4.2.0
beautifulsoup4==4.12.2
bidict==0.23.1
billiard==4.2.1
bitarray==3.1.0
black==24.8.0
blinker==1.9.0
blockbuster==1.5.24
boto3==1.35.53
botocore==1.35.67
bs4==0.0.1
build==1.2.2.post1
cachetools==5.3.3
celery==5.4.0
certifi==2025.1.31
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.3.2
chroma-hnswlib==0.7.6
chromadb==0.6.2
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cloudpickle==3.1.1
colbert-ai==0.2.21
colorama==0.4.6
colorclass==2.2.2
coloredlogs==15.0.1
comm==0.1.3
compressed_rtf==1.0.6
contourpy==1.0.7
cron-descriptor==1.4.5
cryptography==44.0.1
ctranslate2==4.5.0
cycler==0.11.0
dataclasses-json==0.6.7
datasets==3.3.2
debugpy==1.6.7
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.18
dict-toolbox==3.1.2
dill==0.3.8
distro==1.9.0
Django==5.1.7
django-allauth==65.6.0
django-celery==3.1.17
django-celery-beat==2.7.0
django-celery-results==2.5.1
django-debug-toolbar==5.1.0
django-timezone-field==7.1
dnspython==2.7.0
docker==7.1.0
docx2txt==0.8
dotenv==0.9.9
duckduckgo_search==7.3.2
durationpy==0.9
easygui==0.98.3
ebcdic==1.1.1
ecdsa==0.19.0
einops==0.8.0
emoji==2.14.1
et-xmlfile==1.1.0
eval_type_backport==0.2.2
Events==0.5
executing==1.2.0
extract-msg==0.53.1
fake-useragent==1.5.1
fastapi==0.115.7
faster-whisper==1.1.1
filelock==3.14.0
filetype==1.2.0
firecrawl-py==1.12.0
Flask==3.1.0
Flask-APScheduler==1.13.1
flask-smorest==0.45.0
Flask-SQLAlchemy==3.1.1
flatbuffers==25.2.10
fonttools==4.39.4
forbiddenfruit==0.1.4
fpdf2==2.8.2
frozendict==2.4.6
frozenlist==1.5.0
fs==2.4.16
fsspec==2024.3.1
ftfy==6.2.3
gcp-storage-emulator==2024.8.3
geographiclib==2.0
geopy==2.4.0
git-python==1.0.3
gitdb==4.0.11
GitPython==3.1.43
google-ai-generativelanguage==0.4.0
google-api-core==2.24.1
google-api-python-client==2.162.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.1
google-cloud-core==2.4.2
google-cloud-storage==2.19.0
google-crc32c==1.6.0
google-genai==1.20.0
google-generativeai==0.3.2
google-resumable-media==2.7.2
googleapis-common-protos==1.63.2
greenlet==3.1.1
grpcio==1.67.1
grpcio-status==1.63.0rc1
grpcio-tools==1.62.3
h11==0.14.0
h2==4.2.0
hpack==4.1.0
html5lib==1.1
httpcore==1.0.5
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.1
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.4.0
importlib_resources==6.5.2
iniconfig==2.0.0
ipykernel==6.23.1
ipython==8.13.2
isodate==0.7.2
itsdangerous==2.2.0
jedi==0.18.2
Jinja2==3.1.4
jiter==0.8.2
jmespath==1.0.1
joblib==1.3.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.21.1
jsonschema-specifications==2023.12.1
jsonschema_rs==0.29.1
jupyter_client==8.2.0
jupyter_core==5.3.0
kiwisolver==1.4.4
kombu==5.5.1
kubernetes==32.0.1
langchain==0.3.25
langchain-community==0.3.7
langchain-core==0.3.65
langchain-google-genai==2.1.5
langchain-text-splitters==0.3.8
langdetect==1.0.9
langfuse==2.44.0
langgraph==0.4.8
langgraph-api==0.2.51
langgraph-checkpoint==2.0.26
langgraph-cli==0.3.3
langgraph-prebuilt==0.2.2
langgraph-runtime-inmem==0.2.1
langgraph-sdk==0.1.70
langsmith==0.3.45
lark==1.1.9
lazy-object-proxy==1.9.0
ldap3==2.9.1
loguru==0.7.2
lxml==5.3.1
Mako==1.3.9
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.23.1
matplotlib==3.7.1
matplotlib-inline==0.1.6
mdsplit==0.4.0
mdurl==0.1.2
milvus-lite==2.4.11
mmh3==5.1.0
monotonic==1.6
moto==5.1.0
mpmath==1.3.0
msal==1.31.1
msal-extensions==1.2.0
msgpack==1.0.5
msoffcrypto-tool==5.4.2
multidict==6.1.0
multiprocess==0.70.16
multitasking==0.0.11
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
ninja==********
nltk==3.9.1
numpy==1.24.3
oauthlib==3.2.2
olefile==0.47
oletools==0.60.2
onnxruntime==1.20.1
openai==1.65.1
opencv-python==*********
opencv-python-headless==*********
openpyxl==3.1.5
opensearch-py==2.8.0
opentelemetry-api==1.27.0
opentelemetry-exporter-otlp-proto-common==1.27.0
opentelemetry-exporter-otlp-proto-grpc==1.27.0
opentelemetry-instrumentation==0.48b0
opentelemetry-instrumentation-asgi==0.48b0
opentelemetry-instrumentation-fastapi==0.48b0
opentelemetry-proto==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-semantic-conventions==0.48b0
opentelemetry-util-http==0.48b0
orjson==3.10.15
ormsgpack==1.10.0
outcome==1.3.0.post0
overrides==7.7.0
packaging==23.2
pandas==2.2.3
pandasql==0.7.3
parso==0.8.3
passlib==1.7.4
pathspec==0.12.1
patsy==0.5.3
pcodedmp==1.2.6
peewee==3.17.8
peewee-migrate==1.12.2
pexpect==4.8.0
pgvector==0.3.5
pickleshare==0.7.5
Pillow==9.5.0
platformdirs==3.5.1
playwright==1.49.1
pluggy==1.5.0
polars==0.20.5
pop==23.1.0
pop-config==12.0.2
pop-loop==1.0.6
portalocker==2.10.1
posthog==3.18.0
primp==0.14.0
prompt-toolkit==3.0.38
propcache==0.3.0
proto-plus==1.26.0
protobuf==4.25.3
psutil==5.9.5
psycopg2==2.9.9
psycopg2-binary==2.9.9
ptyprocess==0.7.0
pure-eval==0.2.2
py-partiql-parser==0.6.1
pyarrow==16.0.0
pyasn1==0.4.8
pyasn1_modules==0.4.1
pyclipper==1.3.0.post6
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
pydeck==0.9.0
pydub==0.25.1
pyee==12.0.0
Pygments==2.15.1
PyJWT==2.10.1
pymdown-extensions==10.14.2
pymilvus==2.5.0
pymongo==4.11.1
PyMySQL==1.1.1
pypandoc==1.13
pyparsing==3.0.9
pypdf==4.3.1
PyPika==0.48.9
pyproject_hooks==1.2.0
PySocks==1.7.1
pytest==8.3.4
pytest-docker==3.1.2
python-crontab==3.2.0
python-dateutil==2.8.2
python-dotenv==1.1.0
python-engineio==4.11.2
python-iso639==2025.2.18
python-jose==3.4.0
python-magic==0.4.27
python-multipart==0.0.18
python-oxmsg==0.0.2
python-pptx==1.0.0
python-socketio==5.11.3
pytube==15.0.0
pytz==2023.3
pyxlsb==1.0.10
PyYAML==6.0
pyzmq==25.1.0
qdrant-client==1.12.2
rank-bm25==0.2.2
RapidFuzz==3.12.1
rapidocr-onnxruntime==1.3.24
red-black-tree-mod==1.22
redis==5.2.1
referencing==0.35.0
regex==2024.11.6
rend==6.5.2
requests==2.31.0
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
responses==0.25.6
RestrictedPython==8.0
rich==13.7.1
rpds-py==0.18.0
rsa==4.9
RTFDE==0.1.2
s3transfer==0.10.4
safetensors==0.5.3
scikit-learn==1.3.2
scipy==1.10.1
seaborn==0.12.2
selenium==4.15.2
sentence-transformers==3.3.1
sentencepiece==0.2.0
shapely==2.0.7
shellingham==1.5.4
simple-websocket==1.1.0
six==1.16.0
smmap==5.0.1
sniffio==1.3.0
sortedcontainers==2.4.0
soundfile==0.13.1
soupsieve==2.5
SQLAlchemy==2.0.32
sqlparse==0.5.3
sse-starlette==2.1.3
stack-data==0.6.2
starlette==0.45.3
statsmodels==0.14.0
streamlit==1.33.0
structlog==25.4.0
sympy==1.13.1
tenacity==8.2.3
threadpoolctl==3.2.0
tiktoken==0.9.0
tokenizers==0.21.0
toml==0.10.2
toolz==0.12.1
torch==2.6.0
tornado==6.3.2
tqdm==4.66.4
traitlets==5.9.0
transformers==4.49.0
trio==0.23.1
trio-websocket==0.11.1
truststore==0.10.1
typer==0.15.2
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
tzlocal==5.2
ujson==5.10.0
unstructured==0.16.17
unstructured-client==0.30.6
uritemplate==4.1.1
urllib3==2.0.7
uvicorn==0.30.6
uvloop==0.21.0
validators==0.34.0
vine==5.1.0
watchdog==4.0.0
watchfiles==1.0.4
wcwidth==0.2.13
webargs==8.6.0
webdriver-manager==4.0.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0
Werkzeug==3.1.3
wrapt==1.17.2
wsproto==1.2.0
xlrd==2.0.1
xlsx2csv==0.8.4
XlsxWriter==3.1.9
xmltodict==0.14.2
xxhash==3.5.0
yarl==1.18.3
yfinance==0.2.54
youtube-transcript-api==0.6.3
zipp==3.21.0
zstandard==0.23.0
