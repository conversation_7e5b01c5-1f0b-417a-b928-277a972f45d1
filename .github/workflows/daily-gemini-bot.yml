name: Daily Gemini Telegram Bot

on:
  schedule:
    # Runs every day at 7:00 AM UTC (adjust timezone as needed)
    - cron: '0 7 * * *'
  
  # Allows manual triggering for testing
  workflow_dispatch:

jobs:
  send-gemini-update:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run Telegram Bot
      env:
        TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
        TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
        GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
      run: python telegram_gemini_bot.py